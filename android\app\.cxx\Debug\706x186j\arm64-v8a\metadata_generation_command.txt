                        -HE:\aymen\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-DANDROID_PLATFORM=android-23
-DANDROID_ABI=arm64-v8a
-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a
-DANDROID_NDK=C:\Android\ndk\25.1.8937393
-DCMAKE_ANDROID_NDK=C:\Android\ndk\25.1.8937393
-DCMAKE_TOOLCHAIN_FILE=C:\Android\ndk\25.1.8937393\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Android\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=E:\aymen\Mobile\projetMobile\trivia\build\app\intermediates\cxx\Debug\706x186j\obj\arm64-v8a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=E:\aymen\Mobile\projetMobile\trivia\build\app\intermediates\cxx\Debug\706x186j\obj\arm64-v8a
-DCMAKE_BUILD_TYPE=Debug
-BE:\aymen\Mobile\projetMobile\trivia\android\app\.cxx\Debug\706x186j\arm64-v8a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2