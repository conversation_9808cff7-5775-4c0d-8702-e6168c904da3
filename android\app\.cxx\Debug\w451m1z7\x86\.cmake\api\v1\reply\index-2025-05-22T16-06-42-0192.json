{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "C:/Android/cmake/3.22.1/bin/cmake.exe", "cpack": "C:/Android/cmake/3.22.1/bin/cpack.exe", "ctest": "C:/Android/cmake/3.22.1/bin/ctest.exe", "root": "C:/Android/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-76ca16862b459da01ba3.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-456bba5b86e5db56693e.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-cca6b421420ae0fc2a1d.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-456bba5b86e5db56693e.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-cca6b421420ae0fc2a1d.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-76ca16862b459da01ba3.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}