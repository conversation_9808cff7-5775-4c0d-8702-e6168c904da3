                        -HE:\aymen\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-DANDROID_PLATFORM=android-23
-D<PERSON>DROID_ABI=x86
-DCMAKE_ANDROID_ARCH_ABI=x86
-DANDROID_NDK=C:\Android\ndk\25.1.8937393
-DCMAKE_ANDROID_NDK=C:\Android\ndk\25.1.8937393
-DCMAKE_TOOLCHAIN_FILE=C:\Android\ndk\25.1.8937393\build\cmake\android.toolchain.cmake
-DC<PERSON><PERSON>_MAKE_PROGRAM=C:\Android\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=E:\aymen\Mobile\projetMobile\trivia\build\app\intermediates\cxx\Debug\706x186j\obj\x86
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=E:\aymen\Mobile\projetMobile\trivia\build\app\intermediates\cxx\Debug\706x186j\obj\x86
-DCMAKE_BUILD_TYPE=Debug
-BE:\aymen\Mobile\projetMobile\trivia\android\app\.cxx\Debug\706x186j\x86
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2